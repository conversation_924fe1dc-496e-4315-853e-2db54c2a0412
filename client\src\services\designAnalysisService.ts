import { supabase } from '@/lib/supabase'
import type { 
  DesignAnalysis, 
  CreateDesignAnalysisData, 
  UpdateDesignAnalysisData,
  DesignUpload,
  CreateDesignUploadData
} from '@/lib/supabase'

/**
 * Service for managing design analysis data in Supabase
 */
export class DesignAnalysisService {
  
  /**
   * Save a new design analysis to the database
   */
  async saveAnalysis(analysisData: CreateDesignAnalysisData): Promise<DesignAnalysis> {
    const { data, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .insert(analysisData)
      .select()
      .single()

    if (error) {
      console.error('Error saving design analysis:', error)
      throw new Error(`Failed to save analysis: ${error.message}`)
    }

    return data
  }

  /**
   * Get all design analyses for the current user
   */
  async getUserAnalyses(userId: string, options?: {
    limit?: number
    offset?: number
    toolType?: string
    isFavorite?: boolean
    orderBy?: 'created_at' | 'updated_at' | 'overall_score'
    orderDirection?: 'asc' | 'desc'
  }): Promise<DesignAnalysis[]> {
    let query = supabase
      .schema('api')
      .from('design_analyses')
      .select('*')
      .eq('user_id', userId)

    // Apply filters
    if (options?.toolType) {
      query = query.eq('tool_type', options.toolType)
    }
    
    if (options?.isFavorite !== undefined) {
      query = query.eq('is_favorite', options.isFavorite)
    }

    // Apply ordering
    const orderBy = options?.orderBy || 'created_at'
    const orderDirection = options?.orderDirection || 'desc'
    query = query.order(orderBy, { ascending: orderDirection === 'asc' })

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit)
    }
    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching user analyses:', error)
      throw new Error(`Failed to fetch analyses: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get a specific design analysis by ID
   */
  async getAnalysisById(id: string): Promise<DesignAnalysis | null> {
    const { data, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      console.error('Error fetching analysis:', error)
      throw new Error(`Failed to fetch analysis: ${error.message}`)
    }

    return data
  }

  /**
   * Update a design analysis
   */
  async updateAnalysis(updateData: UpdateDesignAnalysisData): Promise<DesignAnalysis> {
    const { id, ...updates } = updateData

    const { data, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating analysis:', error)
      throw new Error(`Failed to update analysis: ${error.message}`)
    }

    return data
  }

  /**
   * Delete a design analysis
   */
  async deleteAnalysis(id: string): Promise<void> {
    const { error } = await supabase
      .schema('api')
      .from('design_analyses')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting analysis:', error)
      throw new Error(`Failed to delete analysis: ${error.message}`)
    }
  }

  /**
   * Get recent design analyses (last 10)
   */
  async getRecentAnalyses(): Promise<DesignAnalysis[]> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User not authenticated')
    }

    // TODO: Remove this mock data when backend is available
    if (process.env.NODE_ENV === 'development') {
      return this.getMockRecentAnalyses()
    }

    return this.getUserAnalyses(user.id, {
      limit: 10,
      orderBy: 'created_at',
      orderDirection: 'desc'
    })
  }

  /**
   * Get favorite design analyses
   */
  async getFavoriteAnalyses(): Promise<DesignAnalysis[]> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User not authenticated')
    }

    // TODO: Remove this mock data when backend is available
    if (process.env.NODE_ENV === 'development') {
      return this.getMockFavoriteAnalyses()
    }

    return this.getUserAnalyses(user.id, {
      isFavorite: true,
      orderBy: 'created_at',
      orderDirection: 'desc'
    })
  }

  /**
   * Toggle favorite status of an analysis
   */
  async toggleFavorite(id: string): Promise<DesignAnalysis> {
    // First get the current analysis to toggle its favorite status
    const currentAnalysis = await this.getAnalysisById(id)
    if (!currentAnalysis) {
      throw new Error('Analysis not found')
    }

    const newFavoriteStatus = !currentAnalysis.is_favorite

    const { data, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .update({ is_favorite: newFavoriteStatus })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error toggling favorite:', error)
      throw new Error(`Failed to toggle favorite: ${error.message}`)
    }

    return data
  }

  /**
   * Rename an analysis
   */
  async renameAnalysis(id: string, newName: string): Promise<DesignAnalysis> {
    const { data, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .update({ custom_name: newName.trim() })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error renaming analysis:', error)
      throw new Error(`Failed to rename analysis: ${error.message}`)
    }

    return data
  }

  /**
   * Record view for an analysis
   */
  async recordView(id: string): Promise<void> {
    const currentAnalysis = await this.getAnalysisById(id)
    if (!currentAnalysis) {
      return
    }

    const { error } = await supabase
      .schema('api')
      .from('design_analyses')
      .update({
        view_count: (currentAnalysis.view_count || 0) + 1,
        last_viewed_at: new Date().toISOString()
      })
      .eq('id', id)

    if (error) {
      console.error('Error recording view:', error)
    }
  }

  /**
   * Add tags to an analysis
   */
  async updateTags(id: string, tags: string[]): Promise<DesignAnalysis> {
    const { data, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .update({ tags })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating tags:', error)
      throw new Error(`Failed to update tags: ${error.message}`)
    }

    return data
  }

  /**
   * Add notes to an analysis
   */
  async updateNotes(id: string, notes: string): Promise<DesignAnalysis> {
    const { data, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .update({ notes })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating notes:', error)
      throw new Error(`Failed to update notes: ${error.message}`)
    }

    return data
  }

  /**
   * Save uploaded file information
   */
  async saveUpload(uploadData: CreateDesignUploadData): Promise<DesignUpload> {
    const { data, error } = await supabase
      .schema('api')
      .from('design_uploads')
      .insert(uploadData)
      .select()
      .single()

    if (error) {
      console.error('Error saving upload:', error)
      throw new Error(`Failed to save upload: ${error.message}`)
    }

    return data
  }

  /**
   * Get user's upload history
   */
  async getUserUploads(userId: string, limit = 50): Promise<DesignUpload[]> {
    const { data, error } = await supabase
      .schema('api')
      .from('design_uploads')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching uploads:', error)
      throw new Error(`Failed to fetch uploads: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get analysis statistics for a user
   */
  async getUserStats(userId: string): Promise<{
    totalAnalyses: number
    favoriteAnalyses: number
    averageScore: number
    toolTypeBreakdown: Record<string, number>
    recentActivity: number // analyses in last 7 days
  }> {
    // Get total count and favorites
    const { data: analyses, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .select('overall_score, tool_type, is_favorite, created_at')
      .eq('user_id', userId)

    if (error) {
      console.error('Error fetching user stats:', error)
      throw new Error(`Failed to fetch user stats: ${error.message}`)
    }

    if (!analyses || analyses.length === 0) {
      return {
        totalAnalyses: 0,
        favoriteAnalyses: 0,
        averageScore: 0,
        toolTypeBreakdown: {},
        recentActivity: 0
      }
    }

    const totalAnalyses = analyses.length
    const favoriteAnalyses = analyses.filter(a => a.is_favorite).length
    const averageScore = analyses.reduce((sum, a) => sum + a.overall_score, 0) / totalAnalyses
    
    // Tool type breakdown
    const toolTypeBreakdown: Record<string, number> = {}
    analyses.forEach(a => {
      toolTypeBreakdown[a.tool_type] = (toolTypeBreakdown[a.tool_type] || 0) + 1
    })

    // Recent activity (last 7 days)
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    const recentActivity = analyses.filter(a => 
      new Date(a.created_at) > sevenDaysAgo
    ).length

    return {
      totalAnalyses,
      favoriteAnalyses,
      averageScore: Math.round(averageScore * 100) / 100,
      toolTypeBreakdown,
      recentActivity
    }
  }

  /**
   * Get mock recent analyses for development testing
   */
  private getMockRecentAnalyses(): DesignAnalysis[] {
    return [
      {
        id: 'mock-1',
        created_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
        updated_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        user_id: 'mock-user',
        original_filename: 'landing-page-design.png',
        file_size: 1024 * 500, // 500KB
        file_type: 'image/png',
        file_url: null,
        tool_type: 'visual_complexity_analyzer',
        analysis_version: '1.0',
        overall_score: 85,
        complexity_scores: {
          color: 8,
          layout: 9,
          typography: 7,
          elements: 8,
          hierarchy: 9,
          composition: 8,
          contrast: 7,
          whitespace: 8
        },
        analysis_areas: [
          {
            name: 'Jerarquía Visual',
            score: 9,
            description: 'Excelente estructura jerárquica con puntos focales claros',
            recommendations: ['Mantener la jerarquía actual']
          },
          {
            name: 'Composición y Balance',
            score: 8,
            description: 'Buen equilibrio visual entre elementos',
            recommendations: ['Considerar ajustar el espaciado entre secciones']
          }
        ],
        recommendations: [
          {
            category: 'Tipografía',
            issue: 'Contraste de texto',
            importance: 'media' as const,
            recommendation: 'Aumentar el contraste del texto secundario'
          }
        ],
        ai_analysis_summary: 'Diseño bien estructurado con excelente jerarquía visual',
        gemini_analysis: 'Análisis detallado por Gemini Vision',
        agent_message: 'Tu diseño muestra un excelente balance visual',
        visuai_insights: ['Diseño profesional', 'Buena legibilidad', 'Estructura clara'],
        analysis_duration_ms: 3500,
        status: 'completed' as const,
        error_message: null,
        is_favorite: false,
        custom_name: 'Landing Page Principal',
        tags: ['landing', 'web', 'corporativo'],
        notes: 'Diseño para la página principal del sitio web',
        view_count: 5,
        last_viewed_at: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        regeneration_count: 1
      },
      {
        id: 'mock-2',
        created_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
        updated_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
        user_id: 'mock-user',
        original_filename: 'mobile-app-ui.jpg',
        file_size: 1024 * 750, // 750KB
        file_type: 'image/jpeg',
        file_url: null,
        tool_type: 'visual_complexity_analyzer',
        analysis_version: '1.0',
        overall_score: 72,
        complexity_scores: {
          color: 6,
          layout: 7,
          typography: 8,
          elements: 6,
          hierarchy: 7,
          composition: 7,
          contrast: 8,
          whitespace: 6
        },
        analysis_areas: [
          {
            name: 'Armonía Cromática',
            score: 6,
            description: 'La paleta de colores podría ser más cohesiva',
            recommendations: ['Reducir la cantidad de colores', 'Usar una paleta más consistente']
          }
        ],
        recommendations: [
          {
            category: 'Color',
            issue: 'Demasiados colores',
            importance: 'alta' as const,
            recommendation: 'Simplificar la paleta de colores'
          }
        ],
        ai_analysis_summary: 'Interfaz móvil con potencial de mejora en la armonía cromática',
        gemini_analysis: null,
        agent_message: 'Tu diseño móvil tiene buena estructura pero puede mejorar en colores',
        visuai_insights: ['Interfaz móvil', 'Necesita simplificación', 'Buena tipografía'],
        analysis_duration_ms: 2800,
        status: 'completed' as const,
        error_message: null,
        is_favorite: true,
        custom_name: 'App Móvil - Dashboard',
        tags: ['mobile', 'app', 'dashboard'],
        notes: 'Diseño del dashboard principal de la aplicación móvil',
        view_count: 12,
        last_viewed_at: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(), // 3 hours ago
        regeneration_count: 0
      },
      {
        id: 'mock-3',
        created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(), // 3 days ago
        updated_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(),
        user_id: 'mock-user',
        original_filename: 'poster-evento.png',
        file_size: 1024 * 1200, // 1.2MB
        file_type: 'image/png',
        file_url: null,
        tool_type: 'visual_complexity_analyzer',
        analysis_version: '1.0',
        overall_score: 58,
        complexity_scores: {
          color: 4,
          layout: 5,
          typography: 6,
          elements: 4,
          hierarchy: 5,
          composition: 6,
          contrast: 7,
          whitespace: 4
        },
        analysis_areas: [
          {
            name: 'Espacio en Blanco',
            score: 4,
            description: 'El diseño se siente saturado, necesita más respiración',
            recommendations: ['Aumentar espacios entre elementos', 'Reducir cantidad de información']
          }
        ],
        recommendations: [
          {
            category: 'Layout',
            issue: 'Sobrecarga visual',
            importance: 'alta' as const,
            recommendation: 'Simplificar el diseño y dar más espacio a los elementos'
          }
        ],
        ai_analysis_summary: 'Poster con alta complejidad visual que necesita simplificación',
        gemini_analysis: null,
        agent_message: 'Tu poster tiene mucha información, considera simplificar',
        visuai_insights: ['Diseño complejo', 'Necesita simplificación', 'Buena información'],
        analysis_duration_ms: 4200,
        status: 'completed' as const,
        error_message: null,
        is_favorite: false,
        custom_name: null,
        tags: ['poster', 'evento', 'marketing'],
        notes: null,
        view_count: 3,
        last_viewed_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(), // 2 days ago
        regeneration_count: 2
      }
    ]
  }

  /**
   * Get mock favorite analyses for development testing
   */
  private getMockFavoriteAnalyses(): DesignAnalysis[] {
    const allMock = this.getMockRecentAnalyses()
    return allMock.filter(analysis => analysis.is_favorite)
  }
}

// Export singleton instance
export const designAnalysisService = new DesignAnalysisService()
