/**
 * Test script to verify the Visual Complexity Analyzer initialization fix
 * Run this in the browser console to test the component loading
 */

console.log('🧪 Testing Visual Complexity Analyzer initialization fix...');

// Test 1: Check if the component is rendered without errors
function testComponentRendering() {
  console.log('\n📋 Test 1: Component Rendering');
  
  try {
    // Look for the Visual Complexity Analyzer component
    const analyzerComponent = document.querySelector('[data-testid="visual-complexity-analyzer"], .design-complexity-analyzer, [class*="complexity"]');
    
    if (analyzerComponent) {
      console.log('✅ Visual Complexity Analyzer component found');
      return true;
    } else {
      console.log('❌ Visual Complexity Analyzer component not found');
      return false;
    }
  } catch (error) {
    console.log('❌ Error checking component:', error.message);
    return false;
  }
}

// Test 2: Check for React errors in console
function testForReactErrors() {
  console.log('\n🔍 Test 2: React Error Check');
  
  // Check if there are any React error boundaries triggered
  const errorElements = document.querySelectorAll('[data-react-error], .react-error, [class*="error"]');
  
  if (errorElements.length === 0) {
    console.log('✅ No React error boundaries detected');
    return true;
  } else {
    console.log('❌ React error boundaries found:', errorElements.length);
    return false;
  }
}

// Test 3: Check if tabs are functional
function testTabFunctionality() {
  console.log('\n📑 Test 3: Tab Functionality');
  
  try {
    // Look for tab elements
    const tabs = document.querySelectorAll('[role="tablist"] button, .tab, [data-value]');
    
    if (tabs.length > 0) {
      console.log(`✅ Found ${tabs.length} tabs`);
      
      // Check for History and Favorites tabs specifically
      const historyTab = Array.from(tabs).find(tab => 
        tab.textContent && tab.textContent.includes('Historial')
      );
      const favoritesTab = Array.from(tabs).find(tab => 
        tab.textContent && tab.textContent.includes('Favoritos')
      );
      
      if (historyTab && favoritesTab) {
        console.log('✅ History and Favorites tabs found');
        
        // Check if tab counters are displayed
        const historyCount = historyTab.textContent.match(/\((\d+)\)/);
        const favoritesCount = favoritesTab.textContent.match(/\((\d+)\)/);
        
        if (historyCount && favoritesCount) {
          console.log(`✅ Tab counters working - History: ${historyCount[1]}, Favorites: ${favoritesCount[1]}`);
          return true;
        } else {
          console.log('⚠️ Tab counters not found or not working properly');
          return false;
        }
      } else {
        console.log('❌ History or Favorites tabs not found');
        return false;
      }
    } else {
      console.log('❌ No tabs found');
      return false;
    }
  } catch (error) {
    console.log('❌ Error testing tabs:', error.message);
    return false;
  }
}

// Test 4: Check for initialization errors in console
function testConsoleErrors() {
  console.log('\n🚨 Test 4: Console Error Check');
  
  // This is a manual check - user should look at console for errors
  console.log('📝 Manual check: Look for any "Cannot access before initialization" errors in console');
  console.log('📝 Manual check: Look for any "getRecentAnalyses" or "getFavoriteAnalyses" errors');
  
  return true; // This is a manual test
}

// Run all tests
function runAllTests() {
  console.log('🚀 Running Visual Complexity Analyzer Fix Tests...\n');
  
  const results = {
    componentRendering: testComponentRendering(),
    reactErrors: testForReactErrors(),
    tabFunctionality: testTabFunctionality(),
    consoleErrors: testConsoleErrors()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} - ${test}`);
  });
  
  const allPassed = Object.values(results).every(result => result === true);
  
  if (allPassed) {
    console.log('\n🎉 All tests passed! The initialization fix is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the specific issues above.');
  }
  
  return results;
}

// Auto-run tests when script loads
setTimeout(() => {
  runAllTests();
}, 2000); // Wait 2 seconds for component to load

// Export for manual testing
window.testVisualComplexityFix = runAllTests;

console.log('📋 Test script loaded. Tests will run automatically in 2 seconds.');
console.log('💡 You can also run tests manually with: testVisualComplexityFix()');
